# Trading UI Makefile
# Supports cross-compilation for x64 and ARM64 architectures

# Project name
PROJECT_NAME = trading-ui

# Target architectures
X64_TARGET = x86_64-unknown-linux-gnu
ARM64_TARGET = aarch64-unknown-linux-gnu

# Build directories
X64_DEBUG_DIR = target/$(X64_TARGET)/debug
X64_RELEASE_DIR = target/$(X64_TARGET)/release
ARM64_DEBUG_DIR = target/$(ARM64_TARGET)/debug
ARM64_RELEASE_DIR = target/$(ARM64_TARGET)/release

# Output directories
DIST_DIR = dist
X64_DIST_DIR = $(DIST_DIR)/x64
ARM64_DIST_DIR = $(DIST_DIR)/arm64

# Default target
.PHONY: help
help:
	@echo "Trading UI Build System"
	@echo "======================="
	@echo ""
	@echo "Available targets:"
	@echo "  run              - Run x64 debug version (default)"
	@echo "  dev-run          - Run development version (native target)"
	@echo "  x64              - Build x64 debug version"
	@echo "  arm64            - Build ARM64 debug version"
	@echo "  x64-release      - Build x64 release version"
	@echo "  arm64-release    - Build ARM64 release version"
	@echo "  all              - Build all versions"
	@echo "  clean            - Clean all build artifacts"
	@echo "  dist             - Create distribution packages"
	@echo "  install-targets  - Install required Rust targets"
	@echo "  check            - Run cargo check for all targets"
	@echo ""

# Install required Rust targets
.PHONY: install-targets
install-targets:
	@echo "Installing Rust targets..."
	rustup target add $(X64_TARGET)
	rustup target add $(ARM64_TARGET)

# Build x64 debug
.PHONY: x64
x64:
	@echo "Building x64 debug version..."
	cargo build --target $(X64_TARGET)
	@echo "✓ x64 debug build complete: $(X64_DEBUG_DIR)/$(PROJECT_NAME)"

# Build ARM64 debug
.PHONY: arm64
arm64:
	@echo "Building ARM64 debug version..."
	cargo build --target $(ARM64_TARGET)
	@echo "✓ ARM64 debug build complete: $(ARM64_DEBUG_DIR)/$(PROJECT_NAME)"

# Build x64 release
.PHONY: x64-release
x64-release:
	@echo "Building x64 release version..."
	cargo build --release --target $(X64_TARGET)
	@echo "✓ x64 release build complete: $(X64_RELEASE_DIR)/$(PROJECT_NAME)"

# Build ARM64 release
.PHONY: arm64-release
arm64-release:
	@echo "Building ARM64 release version..."
	cargo build --release --target $(ARM64_TARGET)
	@echo "✓ ARM64 release build complete: $(ARM64_RELEASE_DIR)/$(PROJECT_NAME)"

# Build all versions
.PHONY: all
all: x64 arm64 x64-release arm64-release
	@echo "✓ All builds complete!"

# Create distribution packages
.PHONY: dist
dist: x64-release arm64-release
	@echo "Creating distribution packages..."
	@mkdir -p $(X64_DIST_DIR) $(ARM64_DIST_DIR)
	@cp $(X64_RELEASE_DIR)/$(PROJECT_NAME) $(X64_DIST_DIR)/$(PROJECT_NAME)
	@cp $(ARM64_RELEASE_DIR)/$(PROJECT_NAME) $(ARM64_DIST_DIR)/$(PROJECT_NAME)
	@cp README.md $(X64_DIST_DIR)/ 2>/dev/null || true
	@cp README.md $(ARM64_DIST_DIR)/ 2>/dev/null || true
	@echo "✓ Distribution packages created in $(DIST_DIR)/"

# Run cargo check for all targets
.PHONY: check
check:
	@echo "Checking x64 target..."
	cargo check --target $(X64_TARGET)
	@echo "Checking ARM64 target..."
	cargo check --target $(ARM64_TARGET)
	@echo "✓ All checks passed!"

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	cargo clean
	rm -rf $(DIST_DIR)
	@echo "✓ Clean complete!"

# Run tests
.PHONY: test
test:
	@echo "Running tests..."
	cargo test
	@echo "✓ Tests complete!"

# Format code
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	cargo fmt
	@echo "✓ Code formatted!"

# Run clippy linter
.PHONY: clippy
clippy:
	@echo "Running clippy..."
	cargo clippy -- -D warnings
	@echo "✓ Clippy checks passed!"

# Development build (native target)
.PHONY: dev
dev:
	@echo "Building for development (native target)..."
	cargo build
	@echo "✓ Development build complete!"

# Run x64 debug version (default)
.PHONY: run
run: x64
	@echo "Running x64 debug version..."
	@echo "Press Ctrl+C to exit"
	./$(X64_DEBUG_DIR)/$(PROJECT_NAME)

# Development run (native target)
.PHONY: dev-run
dev-run:
	@echo "Running development version (native target)..."
	cargo run

# Show binary information
.PHONY: info
info:
	@echo "Binary Information:"
	@echo "=================="
	@if [ -f "$(X64_DEBUG_DIR)/$(PROJECT_NAME)" ]; then \
		echo "x64 debug: $(X64_DEBUG_DIR)/$(PROJECT_NAME)"; \
		file $(X64_DEBUG_DIR)/$(PROJECT_NAME); \
		ls -lh $(X64_DEBUG_DIR)/$(PROJECT_NAME); \
		echo ""; \
	fi
	@if [ -f "$(X64_RELEASE_DIR)/$(PROJECT_NAME)" ]; then \
		echo "x64 release: $(X64_RELEASE_DIR)/$(PROJECT_NAME)"; \
		file $(X64_RELEASE_DIR)/$(PROJECT_NAME); \
		ls -lh $(X64_RELEASE_DIR)/$(PROJECT_NAME); \
		echo ""; \
	fi
	@if [ -f "$(ARM64_DEBUG_DIR)/$(PROJECT_NAME)" ]; then \
		echo "ARM64 debug: $(ARM64_DEBUG_DIR)/$(PROJECT_NAME)"; \
		file $(ARM64_DEBUG_DIR)/$(PROJECT_NAME); \
		ls -lh $(ARM64_DEBUG_DIR)/$(PROJECT_NAME); \
		echo ""; \
	fi
	@if [ -f "$(ARM64_RELEASE_DIR)/$(PROJECT_NAME)" ]; then \
		echo "ARM64 release: $(ARM64_RELEASE_DIR)/$(PROJECT_NAME)"; \
		file $(ARM64_RELEASE_DIR)/$(PROJECT_NAME); \
		ls -lh $(ARM64_RELEASE_DIR)/$(PROJECT_NAME); \
		echo ""; \
	fi

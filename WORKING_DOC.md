# 交易UI - 工作文档

## 项目概述

基于Rust构建的终端交易用户界面，支持多个交易所（币安、Bybit、OKX）的现货和期货交易。应用程序在TUI（终端用户界面）环境中提供实时市场数据、订单管理和投资组合跟踪功能。

## 架构设计

### 核心组件

```
src/
├── main.rs              # 应用程序入口点和事件循环
├── state.rs             # 全局应用状态管理
├── types.rs             # 数据结构和类型定义
├── commands.rs          # 命令解析和执行
├── ui/
│   └── components.rs    # UI渲染组件
├── websocket/
│   ├── binance.rs       # 币安WebSocket客户端
│   ├── handler.rs       # WebSocket消息处理
│   └── server.rs        # WebSocket服务器管理
├── data/
│   ├── mock.rs          # 测试用模拟数据生成
│   └── mod.rs           # 数据模块导出
└── utils/
    ├── logging.rs       # 日志配置
    └── validation.rs    # 输入验证工具
```

### 状态管理

应用程序使用集中式状态管理系统（`SharedState`），包含以下关键组件：

- **市场数据**: 订单簿、交易记录、K线数据
- **投资组合**: 持仓、盈亏跟踪
- **UI状态**: 输入模式、选中标签页、状态消息
- **交易对跟踪**: 变更时间和初始价格（用于状态栏）

## 功能特性

### 1. 多标签页界面

- **币安标签页**: 主要交易界面，包含现货/期货数据
- **持仓标签页**: 投资组合概览，显示所有持仓
- **K线标签页**: 价格图表和成交量分析
- **订单簿标签页**: 详细订单簿和交易历史
- **操作标签页**: 命令执行和系统操作

### 2. 订单簿标签页功能

#### 布局结构
```
┌─ 交易对输入 ───────────────────────────────────────┐
├─ 订单簿行 ────────────────────────────────────────┤
│  ┌─ 现货订单簿 ─────┐  ┌─ 期货订单簿 ─────┐    │
│  │ 10档深度        │  │ 10档深度        │    │
│  └──────────────────┘  └─────────────────────┘    │
├─ 交易行 ──────────────────────────────────────────┤
│  ┌─ 现货交易 ───────┐  ┌─ 期货交易 ───────┐    │
│  │ 最近交易记录     │  │ 最近交易记录     │    │
│  └──────────────────┘  └─────────────────────┘    │
├─ 状态栏 ──────────────────────────────────────────┤
│  ┌─ 交易对状态 ────┐┌─ 现货涨跌 ──┐┌─ 期货涨跌──┐│
│  │ 时间经过       ││ 价格变化   ││ 价格变化  ││
│  └────────────────┘└─────────────┘└───────────────┘│
└────────────────────────────────────────────────────┘
```

#### 交易显示格式
```
方向 |      价格      |   数量   |    金额    | 时间
──────────────────────────────────────────────────────────────
🟢 买入 | 43250.12345678 |   0.1250 |  5406.2656 | 14:30:25
🔴 卖出 | 43248.87654321 |   0.0750 |  3243.6657 | 14:30:24
```

#### Status Bar Features
- **Symbol Status**: Current symbol and time elapsed since symbol change
- **Spot Rate**: Initial price, current price, and percentage change (±X.XXX%)
- **Futures Rate**: Initial price, current price, and percentage change (±X.XXX%)
- **Color Coding**: Green for positive changes, red for negative changes

### 3. Real-time Data Updates

- **OrderBook**: 10-level bid/ask updates
- **Trades**: Recent trade history with amount calculation
- **Price Tracking**: Automatic initial price capture and change calculation
- **Status Updates**: Real-time elapsed time and price change monitoring

## Build System

### Makefile Targets

```bash
# Development
make run              # Build and run x64 debug version
make dev-run          # Run native development version

# Building
make x64              # Build x64 debug version
make arm64            # Build ARM64 debug version
make x64-release      # Build x64 release version
make arm64-release    # Build ARM64 release version
make all              # Build all versions

# Utilities
make clean            # Clean build artifacts
make check            # Run cargo check for all targets
make dist             # Create distribution packages
make install-targets  # Install required Rust targets
make help             # Show available targets
```

### Cross-compilation Support

The project supports cross-compilation for multiple architectures:

- **x86_64-unknown-linux-gnu**: Standard x64 Linux
- **aarch64-unknown-linux-gnu**: ARM64 Linux

Configuration in `.cargo/config.toml`:
```toml
[target.aarch64-unknown-linux-gnu]
linker = "aarch64-linux-gnu-gcc"

[target.x86_64-unknown-linux-gnu]
linker = "gcc"
```

## Key Data Structures

### Trade
```rust
pub struct Trade {
    pub id: String,
    pub symbol: String,
    pub price: f64,
    pub qty: f64,
    pub side: String,        // "BUY" or "SELL"
    pub timestamp: DateTime<Utc>,
    pub is_maker: bool,
}
```

### OrderBook
```rust
pub struct OrderBook {
    pub bids: Vec<(f64, f64)>,  // price, quantity
    pub asks: Vec<(f64, f64)>,
    pub last_update_id: u64,
    pub symbol: String,
}
```

### SharedState (Key Fields)
```rust
pub struct SharedState {
    // Market data
    pub spot_books: DashMap<String, OrderBook>,
    pub fut_books: DashMap<String, OrderBook>,
    pub spot_trades: DashMap<String, VecDeque<Trade>>,
    pub fut_trades: DashMap<String, VecDeque<Trade>>,

    // Symbol tracking for status bar
    pub symbol_change_time: Mutex<Option<DateTime<Utc>>>,
    pub symbol_initial_price: Mutex<Option<f64>>,

    // UI state
    pub selected_symbol: Mutex<String>,
    pub input_mode: Mutex<InputMode>,
    pub selected_tab: Mutex<usize>,
}
```

## Recent Enhancements

### Status Bar Implementation
- Added symbol change time tracking
- Implemented initial price capture on symbol change
- Real-time price change calculation with 3-decimal precision
- Separate tracking for spot and futures markets

### Trade Module Enhancement
- Added Amount column (Price × Qty) to trade displays
- Improved formatting with proper alignment
- Enhanced visual separation with extended divider lines
- Consistent 4-decimal precision for amounts

### Build System Improvements
- Complete Makefile with cross-compilation support
- Automated build and run workflows
- Distribution package creation
- Multi-architecture support (x64, ARM64)

## Development Workflow

1. **Development**: Use `make run` for quick build and test
2. **Cross-platform**: Use `make arm64` for ARM64 builds
3. **Production**: Use `make x64-release` for optimized builds
4. **Distribution**: Use `make dist` to create packages

## Dependencies

Key Rust crates used:
- `ratatui`: Terminal UI framework
- `crossterm`: Cross-platform terminal manipulation
- `tokio`: Async runtime
- `tungstenite`: WebSocket client
- `serde`: Serialization/deserialization
- `chrono`: Date and time handling
- `dashmap`: Concurrent hash map
- `tracing`: Structured logging

## User Interface Controls

### Navigation
- **Tab**: Switch between tabs (Binance → Positions → K-Line → OrderBook → Operations)
- **q**: Quit application
- **s**: Enter symbol input mode (in OrderBook tab)
- **Esc**: Exit input mode

### Symbol Input
- Type symbol name (e.g., "BTCUSDT", "ETHUSDT")
- **Enter**: Confirm symbol selection
- **Esc**: Cancel input

### Visual Indicators
- **🟢**: Buy orders/positive changes
- **🔴**: Sell orders/negative changes
- **Color coding**: Green (profit/up), Red (loss/down), White (neutral)

## State Management Details

### Symbol Tracking Methods
```rust
// Set new symbol and record change time
pub fn set_selected_symbol(&self, symbol: String)

// Track initial price for change calculation
pub fn set_symbol_initial_price(&self, price: f64)

// Get time elapsed since symbol change
pub fn get_symbol_change_time(&self) -> Option<DateTime<Utc>>

// Get initial price for comparison
pub fn get_symbol_initial_price(&self) -> Option<f64>
```

### OrderBook Update Logic
```rust
pub fn update_orderbook(&self, symbol: &str, orderbook: OrderBook, is_futures: bool) {
    // Auto-capture initial price for current symbol
    if !is_futures && symbol == self.get_selected_symbol() {
        if self.get_symbol_initial_price().is_none() {
            let mid_price = (orderbook.asks[0].0 + orderbook.bids[0].0) / 2.0;
            self.set_symbol_initial_price(mid_price);
        }
    }
    // Update orderbook data...
}
```

## Performance Considerations

### Memory Management
- Uses `DashMap` for concurrent access to market data
- `VecDeque` for efficient trade history management
- Limited trade history (10 recent trades displayed)
- Automatic cleanup of old data

### Rendering Optimization
- Selective updates based on data changes
- Efficient string formatting for price displays
- Minimal redraws for status bar updates

## Error Handling

### WebSocket Connections
- Automatic reconnection on connection loss
- Graceful handling of malformed messages
- Fallback to mock data when connections fail

### Input Validation
- Symbol format validation
- Numeric input bounds checking
- Safe parsing of market data

## Testing Strategy

### Mock Data Generation
- Realistic price movements
- Simulated order book updates
- Generated trade sequences
- Volume and PnL calculations

### Build Verification
- Cross-compilation testing
- Debug vs release builds
- Multi-architecture validation

## Deployment

### Binary Distribution
```bash
# Create distribution packages
make dist

# Output structure:
dist/
├── x64/
│   ├── trading-ui      # x64 binary
│   └── README.md
└── arm64/
    ├── trading-ui      # ARM64 binary
    └── README.md
```

### System Requirements
- **Linux**: x86_64 or aarch64
- **Terminal**: UTF-8 support recommended
- **Memory**: ~10MB runtime usage
- **Network**: Optional (for real exchange connections)

## Troubleshooting

### Common Issues
1. **Build Failures**: Ensure cross-compilation tools installed
2. **Display Issues**: Check terminal size (minimum 80x24)
3. **Symbol Not Found**: Verify symbol format (e.g., "BTCUSDT")
4. **Performance**: Reduce update frequency for slower systems

### Debug Mode
```bash
# Run with debug logging
RUST_LOG=debug make run

# Check compilation
make check
```

## Future Enhancements

### Planned Features
- [ ] Real WebSocket connections to exchanges
- [ ] Order placement functionality
- [ ] Advanced charting capabilities
- [ ] Configuration file support
- [ ] Historical data analysis
- [ ] Alert system
- [ ] Portfolio analytics
- [ ] Multi-exchange arbitrage detection
- [ ] Risk management tools
- [ ] Export functionality (CSV, JSON)

### Technical Improvements
- [ ] Async UI updates
- [ ] Memory usage optimization
- [ ] Plugin architecture
- [ ] REST API integration
- [ ] Database persistence
- [ ] Unit test coverage
- [ ] Integration tests
- [ ] Performance benchmarks

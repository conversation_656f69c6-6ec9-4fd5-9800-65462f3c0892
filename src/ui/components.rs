use crate::commands::CommandParser;
use crate::state::SharedState;
use crate::types::{InputMode, Kline, OrderBook, Position, Trade};
use dashmap::DashMap;
use ratatui::{
    Frame,
    backend::Backend,
    layout::{Alignment, Constraint, Direction, Layout, Rect},
    style::{Color, Modifier, Style},
    text::{Line, Span},
    widgets::{Block, Borders, Paragraph, Row, Table, Wrap},
};
use std::collections::VecDeque;

/// 渲染主布局
pub fn render_main_layout<B: Backend>(f: &mut Frame<B>, state: &SharedState) {
    let main_chunks = Layout::default()
        .direction(Direction::Vertical)
        .margin(1)
        .constraints([
            Constraint::Length(1), // Tab bar
            Constraint::Min(0),    // Main content
            Constraint::Length(3), // Command input / status
        ])
        .split(f.size());

    // Tab bar
    render_tab_bar(f, main_chunks[0], state.get_selected_tab());

    // Main content based on selected tab
    match state.get_selected_tab() {
        0 => render_binance_tab(f, main_chunks[1], state),
        1 => render_positions_tab(f, main_chunks[1], state),
        2 => render_kline_tab(f, main_chunks[1], state),
        3 => render_orderbook_trade_tab(f, main_chunks[1], state),
        4 => render_operations_tab(f, main_chunks[1], state),
        _ => {}
    }

    // Command input / status bar
    render_command_bar(f, main_chunks[2], state);
}

/// 渲染标签栏
fn render_tab_bar<B: Backend>(f: &mut Frame<B>, area: Rect, selected: usize) {
    let tabs = vec!["Binance", "Positions", "K-Line", "OrderBook", "Operations"];
    let tab_titles: Vec<Line> = tabs
        .iter()
        .enumerate()
        .map(|(i, &tab)| {
            if i == selected {
                Line::from(Span::styled(
                    format!(" {} ", tab),
                    Style::default()
                        .fg(Color::Yellow)
                        .add_modifier(Modifier::BOLD),
                ))
            } else {
                Line::from(Span::styled(
                    format!(" {} ", tab),
                    Style::default().fg(Color::White),
                ))
            }
        })
        .collect();

    let mut combined_line = Line::default();
    for (i, line) in tab_titles.iter().enumerate() {
        if i > 0 {
            combined_line.spans.push(Span::raw(" | "));
        }
        combined_line.spans.extend(line.spans.clone());
    }
    let tabs_widget = Paragraph::new(combined_line)
        .style(Style::default().fg(Color::White))
        .alignment(Alignment::Left);
    f.render_widget(tabs_widget, area);
}

/// 渲染Binance标签页
fn render_binance_tab<B: Backend>(f: &mut Frame<B>, area: Rect, state: &SharedState) {
    let chunks = Layout::default()
        .direction(Direction::Vertical)
        .constraints([
            Constraint::Percentage(50), // Spot
            Constraint::Percentage(50), // Futures
        ])
        .split(area);

    // Spot section
    let spot_chunks = Layout::default()
        .direction(Direction::Horizontal)
        .constraints([
            Constraint::Percentage(40), // Depth
            Constraint::Percentage(30), // Trades
            Constraint::Percentage(30), // PnL
        ])
        .split(chunks[0]);

    render_orderbook(f, spot_chunks[0], "BN Spot Depth", &state.spot_books, state);
    render_trades(f, spot_chunks[1], "Market Trades", &state.spot_trades);
    render_pnl(f, spot_chunks[2], "Spot PnL", state.spot_unreal_pnl);

    // Futures section
    let fut_chunks = Layout::default()
        .direction(Direction::Horizontal)
        .constraints([
            Constraint::Percentage(40), // Depth
            Constraint::Percentage(30), // My Trades
            Constraint::Percentage(30), // PnL
        ])
        .split(chunks[1]);

    render_orderbook(
        f,
        fut_chunks[0],
        "BN Futures Depth",
        &state.fut_books,
        state,
    );
    render_trades(f, fut_chunks[1], "My Trades", &state.fut_my_trades);
    render_pnl(f, fut_chunks[2], "Futures PnL", state.fut_unreal_pnl);
}

/// 渲染持仓标签页
fn render_positions_tab<B: Backend>(f: &mut Frame<B>, area: Rect, state: &SharedState) {
    let positions: Vec<Position> = state
        .positions
        .iter()
        .map(|entry| entry.value().clone())
        .collect();

    let rows: Vec<Row> = positions
        .iter()
        .map(|pos| {
            Row::new(vec![
                pos.exchange.clone(),
                pos.symbol.clone(),
                format!("{:.4}", pos.size),
                format!("{:.2}", pos.entry_price),
                format!("{:.2}", pos.mark_price),
                format!("{:.2}", pos.unrealized_pnl),
                pos.side.clone(),
            ])
        })
        .collect();

    let table = Table::new(rows)
        .widths(&[
            Constraint::Length(10), // Exchange
            Constraint::Length(10), // Symbol
            Constraint::Length(12), // Size
            Constraint::Length(12), // Entry Price
            Constraint::Length(12), // Mark Price
            Constraint::Length(12), // Unrealized PnL
            Constraint::Length(8),  // Side
        ])
        .header(
            Row::new(vec![
                "Exchange", "Symbol", "Size", "Entry", "Mark", "PnL", "Side",
            ])
            .style(
                Style::default()
                    .fg(Color::Yellow)
                    .add_modifier(Modifier::BOLD),
            ),
        )
        .block(
            Block::default()
                .title("All Positions")
                .borders(Borders::ALL),
        );

    f.render_widget(table, area);
}

/// 渲染K线图标签页
fn render_kline_tab<B: Backend>(f: &mut Frame<B>, area: Rect, state: &SharedState) {
    let selected_symbol = state.get_selected_symbol();

    let chunks = Layout::default()
        .direction(Direction::Vertical)
        .constraints([
            Constraint::Length(3),      // Symbol selector
            Constraint::Percentage(50), // Spot klines
            Constraint::Percentage(50), // Futures klines
        ])
        .split(area);

    // Symbol selector
    render_symbol_selector(f, chunks[0], &selected_symbol, state);

    // Spot K线图
    let spot_chunks = Layout::default()
        .direction(Direction::Horizontal)
        .constraints([
            Constraint::Percentage(70), // Price chart
            Constraint::Percentage(30), // Volume chart
        ])
        .split(chunks[1]);

    render_kline_chart(
        f,
        spot_chunks[0],
        &format!("{} Spot Price", selected_symbol),
        &selected_symbol,
        false,
        state,
    );
    render_volume_chart(
        f,
        spot_chunks[1],
        &format!("{} Spot Volume", selected_symbol),
        &selected_symbol,
        false,
        state,
    );

    // Futures K线图
    let futures_chunks = Layout::default()
        .direction(Direction::Horizontal)
        .constraints([
            Constraint::Percentage(70), // Price chart
            Constraint::Percentage(30), // Volume chart
        ])
        .split(chunks[2]);

    render_kline_chart(
        f,
        futures_chunks[0],
        &format!("{} Futures Price", selected_symbol),
        &selected_symbol,
        true,
        state,
    );
    render_volume_chart(
        f,
        futures_chunks[1],
        &format!("{} Futures Volume", selected_symbol),
        &selected_symbol,
        true,
        state,
    );
}

/// 渲染操作标签页
fn render_operations_tab<B: Backend>(f: &mut Frame<B>, area: Rect, _state: &SharedState) {
    let help_text = CommandParser::get_help_text();

    let paragraph = Paragraph::new(help_text.join("\n"))
        .block(
            Block::default()
                .title("Operations Help")
                .borders(Borders::ALL),
        )
        .wrap(Wrap { trim: true });

    f.render_widget(paragraph, area);
}

/// 渲染订单簿
fn render_orderbook<B: Backend>(
    f: &mut Frame<B>,
    area: Rect,
    title: &str,
    books: &DashMap<String, OrderBook>,
    state: &SharedState,
) {
    // 将区域分为两部分：上部分显示详细订单簿，下部分显示统计信息
    let chunks = Layout::default()
        .direction(Direction::Vertical)
        .constraints([
            Constraint::Percentage(75), // 详细订单簿
            Constraint::Percentage(25), // 统计信息
        ])
        .split(area);

    // 渲染详细订单簿
    render_detailed_orderbook(f, chunks[0], title, books);

    // 渲染统计信息
    render_orderbook_stats(f, chunks[1], books, state);
}

/// 渲染详细订单簿（10档买卖盘）
fn render_detailed_orderbook<B: Backend>(
    f: &mut Frame<B>,
    area: Rect,
    title: &str,
    books: &DashMap<String, OrderBook>,
) {
    let mut content = Vec::new();

    for entry in books.iter() {
        let symbol = entry.key();
        let book = entry.value();

        content.push(format!("=== {} ===", symbol.to_uppercase()));
        content.push(" Bid Price  | Bid Amount | Ask Amount | Ask Price ".to_string());
        content.push("─────────────────────────────────────────────────────────".to_string());

        // 获取前10档数据
        let bids = &book.bids;
        let asks = &book.asks;
        let max_levels = 10.max(bids.len()).max(asks.len());

        for i in 0..max_levels {
            let bid_price = if i < bids.len() {
                format!("{:>10.2}", bids[i].0)
            } else {
                "          ".to_string()
            };

            let bid_amount = if i < bids.len() {
                let amount = bids[i].0 * bids[i].1; // price * qty
                format!("{:>10.2}", amount)
            } else {
                "          ".to_string()
            };

            let ask_amount = if i < asks.len() {
                let amount = asks[i].0 * asks[i].1; // price * qty
                format!("{:>10.2}", amount)
            } else {
                "          ".to_string()
            };

            let ask_price = if i < asks.len() {
                format!("{:>10.2}", asks[i].0)
            } else {
                "          ".to_string()
            };

            content.push(format!(
                "{} | {} | {} | {}",
                bid_price, bid_amount, ask_amount, ask_price
            ));
        }
        content.push("".to_string());
    }

    let paragraph = Paragraph::new(content.join("\n"))
        .block(Block::default().title(title).borders(Borders::ALL))
        .wrap(Wrap { trim: true })
        .style(Style::default().fg(Color::White));

    f.render_widget(paragraph, area);
}

/// 渲染订单簿统计信息
fn render_orderbook_stats<B: Backend>(
    f: &mut Frame<B>,
    area: Rect,
    books: &DashMap<String, OrderBook>,
    state: &SharedState,
) {
    // 将统计区域分为两部分：深度统计和24小时交易量统计
    let chunks = Layout::default()
        .direction(Direction::Horizontal)
        .constraints([
            Constraint::Percentage(60), // 深度统计
            Constraint::Percentage(40), // 24小时交易量统计
        ])
        .split(area);

    // 渲染深度统计
    render_depth_volume_stats(f, chunks[0], books);

    // 渲染24小时交易量统计
    render_24h_volume_stats(f, chunks[1], state);
}

/// 渲染深度amount统计
fn render_depth_volume_stats<B: Backend>(
    f: &mut Frame<B>,
    area: Rect,
    books: &DashMap<String, OrderBook>,
) {
    let mut content = Vec::new();

    for entry in books.iter() {
        let symbol = entry.key();
        let book = entry.value();

        // 计算买10档和卖10档的amount总和 (price * qty)
        let bid_amount_sum: f64 = book
            .bids
            .iter()
            .take(10)
            .map(|(price, qty)| price * qty)
            .sum();
        let ask_amount_sum: f64 = book
            .asks
            .iter()
            .take(10)
            .map(|(price, qty)| price * qty)
            .sum();

        // 计算买卖比例
        let total_amount = bid_amount_sum + ask_amount_sum;
        let bid_ratio = if total_amount > 0.0 {
            bid_amount_sum / total_amount * 100.0
        } else {
            0.0
        };
        let ask_ratio = if total_amount > 0.0 {
            ask_amount_sum / total_amount * 100.0
        } else {
            0.0
        };

        // 创建可视化的比例条
        let bid_bar_length = (bid_ratio / 5.0) as usize; // 每5%一个字符
        let ask_bar_length = (ask_ratio / 5.0) as usize;
        let bid_bar = "█".repeat(bid_bar_length);
        let ask_bar = "█".repeat(ask_bar_length);

        content.push(format!("{} Depth Amount:", symbol.to_uppercase()));
        content.push(format!(
            "Bid 10L: {:>8.2} ({:>4.1}%) {}",
            bid_amount_sum, bid_ratio, bid_bar
        ));
        content.push(format!(
            "Ask 10L: {:>8.2} ({:>4.1}%) {}",
            ask_amount_sum, ask_ratio, ask_bar
        ));
        content.push("".to_string());
    }

    let paragraph = Paragraph::new(content.join("\n"))
        .block(Block::default().title("Depth Amount").borders(Borders::ALL))
        .wrap(Wrap { trim: true })
        .style(Style::default().fg(Color::Cyan));

    f.render_widget(paragraph, area);
}

/// 渲染24小时交易量统计
fn render_24h_volume_stats<B: Backend>(f: &mut Frame<B>, area: Rect, state: &SharedState) {
    let mut content = Vec::new();

    // 获取总体统计
    let (total_spot, total_futures, spot_ratio, futures_ratio) = state.get_combined_volume_stats();

    content.push("24H Volume Summary:".to_string());
    content.push(format!("Total: {:>12.2}", total_spot + total_futures));
    content.push("".to_string());

    // 创建可视化比例条
    let spot_bar_length = (spot_ratio / 5.0) as usize;
    let futures_bar_length = (futures_ratio / 5.0) as usize;
    let spot_bar = "█".repeat(spot_bar_length);
    let futures_bar = "█".repeat(futures_bar_length);

    content.push(format!(
        "Spot:    {:>8.2} ({:>4.1}%) {}",
        total_spot, spot_ratio, spot_bar
    ));
    content.push(format!(
        "Futures: {:>8.2} ({:>4.1}%) {}",
        total_futures, futures_ratio, futures_bar
    ));
    content.push("".to_string());

    // 显示各个交易对的详细统计
    let all_stats = state.get_all_24h_volume_stats();
    for stats in all_stats.iter().take(3) {
        // 只显示前3个
        content.push(format!("{}:", stats.symbol.to_uppercase()));
        content.push(format!(
            "  S: {:>6.1} F: {:>6.1}",
            stats.spot_volume, stats.futures_volume
        ));
    }

    let paragraph = Paragraph::new(content.join("\n"))
        .block(Block::default().title("24H Volume").borders(Borders::ALL))
        .wrap(Wrap { trim: true })
        .style(Style::default().fg(Color::Yellow));

    f.render_widget(paragraph, area);
}

/// 渲染交易记录
fn render_trades<B: Backend>(
    f: &mut Frame<B>,
    area: Rect,
    title: &str,
    trades: &DashMap<String, VecDeque<Trade>>,
) {
    let mut content = vec!["Symbol | Side | Price | Qty | Time".to_string()];

    for entry in trades.iter() {
        let symbol = entry.key();
        let trade_list = entry.value();

        for trade in trade_list.iter().take(5) {
            // Show last 5 trades
            content.push(format!(
                "{:>6} | {:>4} | {:>8.2} | {:>6.4} | {}",
                symbol.to_uppercase(),
                trade.side,
                trade.price,
                trade.qty,
                trade.timestamp.format("%H:%M:%S")
            ));
        }
    }

    let paragraph = Paragraph::new(content.join("\n"))
        .block(Block::default().title(title).borders(Borders::ALL))
        .wrap(Wrap { trim: true });

    f.render_widget(paragraph, area);
}

/// 渲染PnL
fn render_pnl<B: Backend>(f: &mut Frame<B>, area: Rect, title: &str, pnl: f64) {
    let color = if pnl >= 0.0 { Color::Green } else { Color::Red };
    let sign = if pnl >= 0.0 { "+" } else { "" };

    let paragraph = Paragraph::new(format!("{}{:.2} USDT", sign, pnl))
        .style(Style::default().fg(color).add_modifier(Modifier::BOLD))
        .block(Block::default().title(title).borders(Borders::ALL))
        .alignment(Alignment::Center);

    f.render_widget(paragraph, area);
}

/// 渲染命令栏
fn render_command_bar<B: Backend>(f: &mut Frame<B>, area: Rect, state: &SharedState) {
    let chunks = Layout::default()
        .direction(Direction::Vertical)
        .constraints([
            Constraint::Length(1), // Command input
            Constraint::Length(1), // Status
        ])
        .split(area);

    // Command input
    let input_style = if state.get_input_mode() == InputMode::Command {
        Style::default().fg(Color::Yellow)
    } else {
        Style::default().fg(Color::Gray)
    };

    let input_text = if state.get_input_mode() == InputMode::Command {
        format!(":{}", state.get_command_input())
    } else {
        "Press ':' to enter command mode, Tab to switch tabs, 'q' to quit".to_string()
    };

    let input = Paragraph::new(input_text)
        .style(input_style)
        .block(Block::default().borders(Borders::ALL));

    f.render_widget(input, chunks[0]);

    // Status
    let status = Paragraph::new(state.get_status())
        .style(Style::default().fg(Color::Cyan))
        .block(Block::default().borders(Borders::ALL));

    f.render_widget(status, chunks[1]);
}

/// 渲染交易对选择器
fn render_symbol_selector<B: Backend>(
    f: &mut Frame<B>,
    area: Rect,
    selected_symbol: &str,
    _state: &SharedState,
) {
    let symbols = vec!["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT", "SOLUSDT"];
    let symbol_text = symbols
        .iter()
        .map(|&symbol| {
            if symbol == selected_symbol {
                format!("[{}]", symbol)
            } else {
                symbol.to_string()
            }
        })
        .collect::<Vec<_>>()
        .join(" | ");

    let paragraph = Paragraph::new(format!("Symbol: {}", symbol_text))
        .style(Style::default().fg(Color::Cyan))
        .block(
            Block::default()
                .title("Select Symbol (Use 1-5 keys)")
                .borders(Borders::ALL),
        )
        .alignment(Alignment::Center);

    f.render_widget(paragraph, area);
}

/// 渲染K线图
fn render_kline_chart<B: Backend>(
    f: &mut Frame<B>,
    area: Rect,
    title: &str,
    symbol: &str,
    is_futures: bool,
    state: &SharedState,
) {
    let klines = state.get_klines(symbol, is_futures);

    if klines.is_empty() {
        let paragraph = Paragraph::new("No data available")
            .style(Style::default().fg(Color::Gray))
            .block(Block::default().title(title).borders(Borders::ALL))
            .alignment(Alignment::Center);
        f.render_widget(paragraph, area);
        return;
    }

    // 计算价格范围
    let mut min_price = f64::MAX;
    let mut max_price = f64::MIN;
    for kline in &klines {
        min_price = min_price.min(kline.low_price);
        max_price = max_price.max(kline.high_price);
    }

    // 添加一些边距
    let price_range = max_price - min_price;
    let margin = price_range * 0.1;
    min_price -= margin;
    max_price += margin;

    // 构建K线图内容
    let mut content = Vec::new();
    content.push(format!("Price Range: {:.2} - {:.2}", min_price, max_price));
    content.push(String::new());

    // 显示最近的K线数据
    let recent_klines = klines.iter().rev().take(10).collect::<Vec<_>>();
    content.push("Recent K-lines (OHLC):".to_string());

    for kline in recent_klines {
        let color_indicator = if kline.close_price >= kline.open_price {
            "↑"
        } else {
            "↓"
        };

        content.push(format!(
            "{} O:{:.2} H:{:.2} L:{:.2} C:{:.2}",
            color_indicator, kline.open_price, kline.high_price, kline.low_price, kline.close_price
        ));
    }

    let paragraph = Paragraph::new(content.join("\n"))
        .style(Style::default().fg(Color::White))
        .block(Block::default().title(title).borders(Borders::ALL))
        .wrap(Wrap { trim: true });

    f.render_widget(paragraph, area);
}

/// 渲染成交量图
fn render_volume_chart<B: Backend>(
    f: &mut Frame<B>,
    area: Rect,
    title: &str,
    symbol: &str,
    is_futures: bool,
    state: &SharedState,
) {
    let klines = state.get_klines(symbol, is_futures);

    if klines.is_empty() {
        let paragraph = Paragraph::new("No data available")
            .style(Style::default().fg(Color::Gray))
            .block(Block::default().title(title).borders(Borders::ALL))
            .alignment(Alignment::Center);
        f.render_widget(paragraph, area);
        return;
    }

    // 计算成交量统计
    let total_volume: f64 = klines.iter().map(|k| k.volume).sum();
    let avg_volume = total_volume / klines.len() as f64;
    let max_volume = klines.iter().map(|k| k.volume).fold(0.0, f64::max);

    let mut content = Vec::new();
    content.push(format!("Total Volume: {:.2}", total_volume));
    content.push(format!("Avg Volume: {:.2}", avg_volume));
    content.push(format!("Max Volume: {:.2}", max_volume));
    content.push(String::new());

    // 显示最近的成交量数据
    content.push("Recent Volumes:".to_string());
    let recent_klines = klines.iter().rev().take(8).collect::<Vec<_>>();

    for kline in recent_klines {
        let volume_bar = if max_volume > 0.0 {
            let ratio = kline.volume / max_volume;
            let bar_length = (ratio * 20.0) as usize;
            "█".repeat(bar_length)
        } else {
            String::new()
        };

        content.push(format!("{:.2} {}", kline.volume, volume_bar));
    }

    let paragraph = Paragraph::new(content.join("\n"))
        .style(Style::default().fg(Color::Cyan))
        .block(Block::default().title(title).borders(Borders::ALL))
        .wrap(Wrap { trim: true });

    f.render_widget(paragraph, area);
}

/// 渲染OrderBook和Trade标签页
fn render_orderbook_trade_tab<B: Backend>(f: &mut Frame<B>, area: Rect, state: &SharedState) {
    let selected_symbol = state.get_selected_symbol();

    let chunks = Layout::default()
        .direction(Direction::Vertical)
        .constraints([
            Constraint::Length(3),      // Symbol input
            Constraint::Percentage(40), // OrderBook row
            Constraint::Percentage(40), // Trade row
            Constraint::Length(3),      // Status bar
        ])
        .split(area);

    // Symbol input/selector
    render_symbol_input(f, chunks[0], state);

    // OrderBook row - 两列显示现货和期货的十档orderbook
    let orderbook_chunks = Layout::default()
        .direction(Direction::Horizontal)
        .constraints([
            Constraint::Percentage(50), // Spot OrderBook
            Constraint::Percentage(50), // Futures OrderBook
        ])
        .split(chunks[1]);

    render_spot_orderbook_only(f, orderbook_chunks[0], &selected_symbol, state);
    render_futures_orderbook_only(f, orderbook_chunks[1], &selected_symbol, state);

    // Trade row - 两列显示现货和期货的trade
    let trade_chunks = Layout::default()
        .direction(Direction::Horizontal)
        .constraints([
            Constraint::Percentage(50), // Spot Trades
            Constraint::Percentage(50), // Futures Trades
        ])
        .split(chunks[2]);

    render_spot_trades_only(f, trade_chunks[0], &selected_symbol, state);
    render_futures_trades_only(f, trade_chunks[1], &selected_symbol, state);

    // Status bar
    render_orderbook_status_bar(f, chunks[3], state);
}

/// 渲染币种输入框
fn render_symbol_input<B: Backend>(f: &mut Frame<B>, area: Rect, state: &SharedState) {
    let input_mode = state.get_input_mode();
    let symbol_input = state.get_symbol_input();
    let selected_symbol = state.get_selected_symbol();

    let (input_text, title, style) = match input_mode {
        crate::types::InputMode::SymbolInput => (
            format!("Input: {}_", symbol_input),
            "Enter Symbol (Press Enter to confirm, Esc to cancel)",
            Style::default()
                .fg(Color::Green)
                .add_modifier(Modifier::BOLD),
        ),
        _ => (
            format!("Symbol: {} (Press 's' to edit)", selected_symbol),
            "Trading Symbol",
            Style::default()
                .fg(Color::Yellow)
                .add_modifier(Modifier::BOLD),
        ),
    };

    let paragraph = Paragraph::new(input_text)
        .style(style)
        .block(Block::default().title(title).borders(Borders::ALL))
        .alignment(Alignment::Left);

    f.render_widget(paragraph, area);
}

/// 渲染现货OrderBook（仅十档）
fn render_spot_orderbook_only<B: Backend>(
    f: &mut Frame<B>,
    area: Rect,
    symbol: &str,
    state: &SharedState,
) {
    let mut content = Vec::new();

    if let Some(book_entry) = state.spot_books.get(symbol) {
        let book = book_entry.value();

        content.push("   Bid Price   |  Bid Amount  |  Ask Amount  |   Ask Price   ".to_string());
        content.push(
            "─────────────────────────────────────────────────────────────────────────".to_string(),
        );

        // 获取前10档数据
        let bids = &book.bids;
        let asks = &book.asks;
        let max_levels = 10.min(bids.len().max(asks.len()));

        for i in 0..max_levels {
            let bid_price = if i < bids.len() {
                format!("{:>12.8}", bids[i].0)
            } else {
                "            ".to_string()
            };

            let bid_amount = if i < bids.len() {
                let amount = bids[i].0 * bids[i].1; // price * qty
                format!("{:>12.2}", amount)
            } else {
                "            ".to_string()
            };

            let ask_amount = if i < asks.len() {
                let amount = asks[i].0 * asks[i].1; // price * qty
                format!("{:>12.2}", amount)
            } else {
                "            ".to_string()
            };

            let ask_price = if i < asks.len() {
                format!("{:>12.8}", asks[i].0)
            } else {
                "            ".to_string()
            };

            content.push(format!(
                "{} | {} | {} | {}",
                bid_price, bid_amount, ask_amount, ask_price
            ));
        }
    } else {
        content.push("No orderbook data available".to_string());
    }

    let paragraph = Paragraph::new(content.join("\n"))
        .block(
            Block::default()
                .title("Spot OrderBook (10 Levels)")
                .borders(Borders::ALL),
        )
        .wrap(Wrap { trim: true })
        .style(Style::default().fg(Color::Green));

    f.render_widget(paragraph, area);
}

/// 渲染期货OrderBook（仅十档）
fn render_futures_orderbook_only<B: Backend>(
    f: &mut Frame<B>,
    area: Rect,
    symbol: &str,
    state: &SharedState,
) {
    let mut content = Vec::new();

    if let Some(book_entry) = state.fut_books.get(symbol) {
        let book = book_entry.value();

        content.push("   Bid Price   |  Bid Amount  |  Ask Amount  |   Ask Price   ".to_string());
        content.push(
            "─────────────────────────────────────────────────────────────────────────".to_string(),
        );

        // 获取前10档数据
        let bids = &book.bids;
        let asks = &book.asks;
        let max_levels = 10.min(bids.len().max(asks.len()));

        for i in 0..max_levels {
            let bid_price = if i < bids.len() {
                format!("{:>12.8}", bids[i].0)
            } else {
                "            ".to_string()
            };

            let bid_amount = if i < bids.len() {
                let amount = bids[i].0 * bids[i].1; // price * qty
                format!("{:>12.2}", amount)
            } else {
                "            ".to_string()
            };

            let ask_amount = if i < asks.len() {
                let amount = asks[i].0 * asks[i].1; // price * qty
                format!("{:>12.2}", amount)
            } else {
                "            ".to_string()
            };

            let ask_price = if i < asks.len() {
                format!("{:>12.8}", asks[i].0)
            } else {
                "            ".to_string()
            };

            content.push(format!(
                "{} | {} | {} | {}",
                bid_price, bid_amount, ask_amount, ask_price
            ));
        }
    } else {
        content.push("No orderbook data available".to_string());
    }

    let paragraph = Paragraph::new(content.join("\n"))
        .block(
            Block::default()
                .title("Futures OrderBook (10 Levels)")
                .borders(Borders::ALL),
        )
        .wrap(Wrap { trim: true })
        .style(Style::default().fg(Color::Yellow));

    f.render_widget(paragraph, area);
}

/// 渲染现货交易记录
fn render_spot_trades_only<B: Backend>(
    f: &mut Frame<B>,
    area: Rect,
    symbol: &str,
    state: &SharedState,
) {
    let mut content = vec!["Side |     Price      |   Qty    |   Amount   | Time".to_string()];
    content.push("──────────────────────────────────────────────────────────────".to_string());

    if let Some(trade_entry) = state.spot_trades.get(symbol) {
        let trade_list = trade_entry.value();

        for trade in trade_list.iter().take(10) {
            // Show last 10 trades
            let side_color = if trade.side == "BUY" { "🟢" } else { "🔴" };
            let amount = trade.price * trade.qty;
            content.push(format!(
                "{} {:>4} | {:>12.8} | {:>8.4} | {:>10.4} | {}",
                side_color,
                trade.side,
                trade.price,
                trade.qty,
                amount,
                trade.timestamp.format("%H:%M:%S")
            ));
        }
    } else {
        content.push("No trade data available".to_string());
    }

    let paragraph = Paragraph::new(content.join("\n"))
        .block(Block::default().title("Spot Trades").borders(Borders::ALL))
        .wrap(Wrap { trim: true })
        .style(Style::default().fg(Color::Green));

    f.render_widget(paragraph, area);
}

/// 渲染期货交易记录
fn render_futures_trades_only<B: Backend>(
    f: &mut Frame<B>,
    area: Rect,
    symbol: &str,
    state: &SharedState,
) {
    let mut content = vec!["Side |     Price      |   Qty    |   Amount   | Time".to_string()];
    content.push("──────────────────────────────────────────────────────────────".to_string());

    if let Some(trade_entry) = state.fut_trades.get(symbol) {
        let trade_list = trade_entry.value();

        for trade in trade_list.iter().take(10) {
            // Show last 10 trades
            let side_color = if trade.side == "BUY" { "🟢" } else { "🔴" };
            let amount = trade.price * trade.qty;
            content.push(format!(
                "{} {:>4} | {:>12.8} | {:>8.4} | {:>10.4} | {}",
                side_color,
                trade.side,
                trade.price,
                trade.qty,
                amount,
                trade.timestamp.format("%H:%M:%S")
            ));
        }
    } else {
        content.push("No trade data available".to_string());
    }

    let paragraph = Paragraph::new(content.join("\n"))
        .block(
            Block::default()
                .title("Futures Trades")
                .borders(Borders::ALL),
        )
        .wrap(Wrap { trim: true })
        .style(Style::default().fg(Color::Yellow));

    f.render_widget(paragraph, area);
}

/// 渲染OrderBook状态栏
fn render_orderbook_status_bar<B: Backend>(f: &mut Frame<B>, area: Rect, state: &SharedState) {
    let selected_symbol = state.get_selected_symbol();

    // 计算时间差
    let time_elapsed = if let Some(change_time) = state.get_symbol_change_time() {
        let now = chrono::Utc::now();
        let duration = now.signed_duration_since(change_time);

        if duration.num_days() > 0 {
            format!(
                "{}d {}h {}m {}s",
                duration.num_days(),
                duration.num_hours() % 24,
                duration.num_minutes() % 60,
                duration.num_seconds() % 60
            )
        } else if duration.num_hours() > 0 {
            format!(
                "{}h {}m {}s",
                duration.num_hours(),
                duration.num_minutes() % 60,
                duration.num_seconds() % 60
            )
        } else if duration.num_minutes() > 0 {
            format!(
                "{}m {}s",
                duration.num_minutes(),
                duration.num_seconds() % 60
            )
        } else {
            format!("{}s", duration.num_seconds())
        }
    } else {
        "N/A".to_string()
    };

    // 获取初始价格
    let initial_price = state.get_symbol_initial_price();

    // 计算现货价格信息
    let (spot_current_price, spot_change_text) = if let Some(initial) = initial_price {
        if let Some(book_entry) = state.spot_books.get(&selected_symbol) {
            let book = book_entry.value();
            if !book.asks.is_empty() && !book.bids.is_empty() {
                let current_price = (book.asks[0].0 + book.bids[0].0) / 2.0;
                let change_percent = ((current_price - initial) / initial) * 100.0;
                let sign = if change_percent > 0.0 { "+" } else { "" };
                (
                    Some(current_price),
                    format!("{}{:.3}%", sign, change_percent),
                )
            } else {
                (None, "No data".to_string())
            }
        } else {
            (None, "No data".to_string())
        }
    } else {
        (None, "Waiting...".to_string())
    };

    // 计算期货价格信息
    let (futures_current_price, futures_change_text) = if let Some(initial) = initial_price {
        if let Some(book_entry) = state.fut_books.get(&selected_symbol) {
            let book = book_entry.value();
            if !book.asks.is_empty() && !book.bids.is_empty() {
                let current_price = (book.asks[0].0 + book.bids[0].0) / 2.0;
                let change_percent = ((current_price - initial) / initial) * 100.0;
                let sign = if change_percent > 0.0 { "+" } else { "" };
                (
                    Some(current_price),
                    format!("{}{:.3}%", sign, change_percent),
                )
            } else {
                (None, "No data".to_string())
            }
        } else {
            (None, "No data".to_string())
        }
    } else {
        (None, "Waiting...".to_string())
    };

    // 分割状态栏为三列
    let chunks = Layout::default()
        .direction(Direction::Horizontal)
        .constraints([
            Constraint::Percentage(40), // 时间信息
            Constraint::Percentage(30), // 现货价格变化
            Constraint::Percentage(30), // 期货价格变化
        ])
        .split(area);

    // 渲染时间信息
    let time_text = format!("Symbol: {} | Time: {}", selected_symbol, time_elapsed);
    let time_paragraph = Paragraph::new(time_text)
        .block(
            Block::default()
                .borders(Borders::ALL)
                .title("Symbol Status"),
        )
        .style(Style::default().fg(Color::Cyan))
        .alignment(Alignment::Left);
    f.render_widget(time_paragraph, chunks[0]);

    // 渲染现货价格信息
    let spot_color = if spot_change_text.starts_with('+') {
        Color::Green
    } else if spot_change_text.starts_with('-') {
        Color::Red
    } else {
        Color::White
    };

    let spot_text = if let (Some(initial), Some(current)) = (initial_price, spot_current_price) {
        format!(
            "Initial: {:.6}\nCurrent: {:.6}\nChange: {}",
            initial, current, spot_change_text
        )
    } else if let Some(initial) = initial_price {
        format!(
            "Initial: {:.6}\nCurrent: N/A\nChange: {}",
            initial, spot_change_text
        )
    } else {
        format!("Initial: N/A\nCurrent: N/A\nChange: {}", spot_change_text)
    };

    let spot_paragraph = Paragraph::new(spot_text)
        .block(Block::default().borders(Borders::ALL).title("Spot Price"))
        .style(Style::default().fg(spot_color))
        .alignment(Alignment::Left);
    f.render_widget(spot_paragraph, chunks[1]);

    // 渲染期货价格信息
    let futures_color = if futures_change_text.starts_with('+') {
        Color::Green
    } else if futures_change_text.starts_with('-') {
        Color::Red
    } else {
        Color::White
    };

    let futures_text =
        if let (Some(initial), Some(current)) = (initial_price, futures_current_price) {
            format!(
                "Initial: {:.6}\nCurrent: {:.6}\nChange: {}",
                initial, current, futures_change_text
            )
        } else if let Some(initial) = initial_price {
            format!(
                "Initial: {:.6}\nCurrent: N/A\nChange: {}",
                initial, futures_change_text
            )
        } else {
            format!(
                "Initial: N/A\nCurrent: N/A\nChange: {}",
                futures_change_text
            )
        };

    let futures_paragraph = Paragraph::new(futures_text)
        .block(
            Block::default()
                .borders(Borders::ALL)
                .title("Futures Price"),
        )
        .style(Style::default().fg(futures_color))
        .alignment(Alignment::Left);
    f.render_widget(futures_paragraph, chunks[2]);
}

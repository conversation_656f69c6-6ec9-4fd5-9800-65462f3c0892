use crate::commands::CommandParser;
use crate::state::SharedState;
use crate::types::{InputMode, UiEvent, WsMessage};
use crate::ui::components::render_main_layout;
use crossterm::event::{KeyCode, KeyEvent};
use ratatui::{Frame, Terminal, backend::Backend};
use tokio::sync::mpsc;

/// 主应用程序结构
pub struct App {
    pub state: SharedState,
    pub should_quit: bool,
}

impl App {
    /// 创建新的应用程序实例
    pub fn new(state: SharedState) -> Self {
        Self {
            state,
            should_quit: false,
        }
    }

    /// 渲染UI
    pub fn render<B: Backend>(&self, f: &mut Frame<B>) {
        render_main_layout(f, &self.state);
    }

    /// 处理键盘事件
    pub async fn handle_key_event(
        &mut self,
        key: KeyEvent,
        ws_tx: &mpsc::UnboundedSender<WsMessage>,
    ) -> bool {
        match self.state.get_input_mode() {
            InputMode::Normal => self.handle_normal_mode_key(key).await,
            InputMode::Command => self.handle_command_mode_key(key, ws_tx).await,
            InputMode::SymbolInput => self.handle_symbol_input_mode_key(key).await,
        }
    }

    /// 处理普通模式下的按键
    async fn handle_normal_mode_key(&mut self, key: KeyEvent) -> bool {
        match key.code {
            KeyCode::Char('q') => {
                self.should_quit = true;
                true
            }
            KeyCode::Char(':') => {
                self.state.set_input_mode(InputMode::Command);
                self.state.clear_command_input();
                false
            }
            KeyCode::Tab => {
                let current_tab = self.state.get_selected_tab();
                self.state.set_selected_tab((current_tab + 1) % 5); // 现在有5个标签页
                false
            }
            KeyCode::Char('h') => {
                // 显示帮助
                self.state.set_status(
                    "Press ':' to enter command mode, Tab to switch tabs, 'q' to quit, 1-5 to select symbol".to_string(),
                );
                false
            }
            KeyCode::Char('1') => {
                self.state.set_selected_symbol("BTCUSDT".to_string());
                self.state.set_status("Selected BTCUSDT".to_string());
                false
            }
            KeyCode::Char('2') => {
                self.state.set_selected_symbol("ETHUSDT".to_string());
                self.state.set_status("Selected ETHUSDT".to_string());
                false
            }
            KeyCode::Char('3') => {
                self.state.set_selected_symbol("ADAUSDT".to_string());
                self.state.set_status("Selected ADAUSDT".to_string());
                false
            }
            KeyCode::Char('4') => {
                self.state.set_selected_symbol("BNBUSDT".to_string());
                self.state.set_status("Selected BNBUSDT".to_string());
                false
            }
            KeyCode::Char('5') => {
                self.state.set_selected_symbol("SOLUSDT".to_string());
                self.state.set_status("Selected SOLUSDT".to_string());
                false
            }
            KeyCode::Char('s') => {
                // 进入symbol输入模式
                self.state.set_input_mode(InputMode::SymbolInput);
                self.state.clear_symbol_input();
                self.state.set_status(
                    "Enter symbol name (press Enter to confirm, Esc to cancel)".to_string(),
                );
                false
            }
            _ => false,
        }
    }

    /// 处理命令模式下的按键
    async fn handle_command_mode_key(
        &mut self,
        key: KeyEvent,
        ws_tx: &mpsc::UnboundedSender<WsMessage>,
    ) -> bool {
        match key.code {
            KeyCode::Enter => {
                let command = self.state.get_command_input();
                self.state.clear_command_input();
                self.state.set_input_mode(InputMode::Normal);

                self.execute_command(&command, ws_tx).await;
                false
            }
            KeyCode::Esc => {
                self.state.set_input_mode(InputMode::Normal);
                self.state.clear_command_input();
                self.state.set_status("Command cancelled".to_string());
                false
            }
            KeyCode::Char(c) => {
                self.state.push_command_char(c);
                false
            }
            KeyCode::Backspace => {
                self.state.pop_command_char();
                false
            }
            _ => false,
        }
    }

    /// 执行命令
    async fn execute_command(&mut self, command: &str, ws_tx: &mpsc::UnboundedSender<WsMessage>) {
        if command.trim().is_empty() {
            return;
        }

        // 特殊命令处理
        if command.trim() == "help" {
            self.show_help();
            return;
        }

        // 验证命令
        if let Err(error) = CommandParser::validate_command(command) {
            self.state.set_status(format!("Error: {}", error));
            return;
        }

        // 解析并发送命令
        if let Some(msg) = CommandParser::parse(command) {
            if let Err(e) = ws_tx.send(msg) {
                self.state
                    .set_status(format!("Failed to send command: {}", e));
            } else {
                self.state.set_status(format!("Sent command: {}", command));
            }
        } else {
            self.state
                .set_status(format!("Invalid command: {}", command));
        }
    }

    /// 处理symbol输入模式下的按键
    async fn handle_symbol_input_mode_key(&mut self, key: KeyEvent) -> bool {
        match key.code {
            KeyCode::Enter => {
                // 确认输入的symbol
                let symbol_input = self.state.get_symbol_input();
                if !symbol_input.is_empty() {
                    let symbol = symbol_input.to_uppercase();
                    self.state.set_selected_symbol(symbol.clone());
                    self.state
                        .set_status(format!("Selected symbol: {}", symbol));
                } else {
                    self.state.set_status("Symbol cannot be empty".to_string());
                }
                self.state.set_input_mode(InputMode::Normal);
                self.state.clear_symbol_input();
                false
            }
            KeyCode::Esc => {
                // 取消输入
                self.state.set_input_mode(InputMode::Normal);
                self.state.clear_symbol_input();
                self.state.set_status("Symbol input cancelled".to_string());
                false
            }
            KeyCode::Backspace => {
                // 删除最后一个字符
                self.state.pop_char_from_symbol_input();
                false
            }
            KeyCode::Char(c) => {
                // 添加字符到输入
                if c.is_alphanumeric() {
                    self.state.add_char_to_symbol_input(c.to_ascii_uppercase());
                }
                false
            }
            _ => false,
        }
    }

    /// 显示帮助信息
    fn show_help(&mut self) {
        self.state
            .set_status("Help displayed in Operations tab".to_string());
        self.state.set_selected_tab(2); // 切换到Operations标签页
    }

    /// 处理UI事件
    pub async fn handle_ui_event(
        &mut self,
        event: UiEvent,
        ws_tx: &mpsc::UnboundedSender<WsMessage>,
    ) -> bool {
        match event {
            UiEvent::KeyPress(key) => self.handle_key_event(key, ws_tx).await,
            UiEvent::Tick => {
                // 定期清理旧数据
                self.state.cleanup_old_trades();
                false
            }
            UiEvent::Resize(_, _) => {
                // 处理终端大小变化
                false
            }
        }
    }

    /// 检查是否应该退出
    pub fn should_quit(&self) -> bool {
        self.should_quit
    }

    /// 更新状态消息
    pub fn set_status_message(&mut self, message: String) {
        self.state.set_status(message);
    }

    /// 获取当前选中的标签页
    pub fn get_selected_tab(&self) -> usize {
        self.state.get_selected_tab()
    }

    /// 设置选中的标签页
    pub fn set_selected_tab(&mut self, tab: usize) {
        self.state.set_selected_tab(tab);
    }
}

/// 运行应用程序的主循环
pub async fn run_app<B: Backend>(
    terminal: &mut Terminal<B>,
    mut app: App,
    mut ui_rx: tokio::sync::broadcast::Receiver<UiEvent>,
    ws_tx: mpsc::UnboundedSender<WsMessage>,
) -> Result<(), Box<dyn std::error::Error>> {
    loop {
        // 渲染UI
        terminal.draw(|f| app.render(f))?;

        // 处理事件
        if let Ok(event) = ui_rx.recv().await {
            if app.handle_ui_event(event, &ws_tx).await {
                break;
            }
        }

        // 检查是否应该退出
        if app.should_quit() {
            break;
        }
    }

    Ok(())
}

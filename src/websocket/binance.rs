use crate::state::SharedState;
use crate::types::{DepthDiff, DepthSnapshot, Kline};
use anyhow::{Result, anyhow};
use futures_util::{SinkExt, StreamExt};
use serde_json::Value;
use std::env;
use std::net::SocketAddr;
use std::pin::Pin;
use std::task::{Context, Poll};

use tokio::net::TcpStream;
use tokio::time::{Duration, sleep};
use tokio_tungstenite::{
    MaybeTlsStream, WebSocketStream, client_async, connect_async, tungstenite::Message,
};
use tracing::{debug, error, info, warn};
use url::Url;

/// 统一的WebSocket流类型
pub enum UnifiedStream {
    Direct(WebSocketStream<MaybeTlsStream<TcpStream>>),
    Proxy(WebSocketStream<MaybeTlsStream<TcpStream>>),
}

impl futures_util::Sink<Message> for UnifiedStream {
    type Error = tungstenite::Error;

    fn poll_ready(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        match self.get_mut() {
            UnifiedStream::Direct(s) => Pin::new(s).poll_ready(cx),
            UnifiedStream::Proxy(s) => Pin::new(s).poll_ready(cx),
        }
    }

    fn start_send(self: Pin<&mut Self>, item: Message) -> Result<(), Self::Error> {
        match self.get_mut() {
            UnifiedStream::Direct(s) => Pin::new(s).start_send(item),
            UnifiedStream::Proxy(s) => Pin::new(s).start_send(item),
        }
    }

    fn poll_flush(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        match self.get_mut() {
            UnifiedStream::Direct(s) => Pin::new(s).poll_flush(cx),
            UnifiedStream::Proxy(s) => Pin::new(s).poll_flush(cx),
        }
    }

    fn poll_close(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        match self.get_mut() {
            UnifiedStream::Direct(s) => Pin::new(s).poll_close(cx),
            UnifiedStream::Proxy(s) => Pin::new(s).poll_close(cx),
        }
    }
}

impl futures_util::Stream for UnifiedStream {
    type Item = Result<Message, tungstenite::Error>;

    fn poll_next(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        match self.get_mut() {
            UnifiedStream::Direct(s) => Pin::new(s).poll_next(cx),
            UnifiedStream::Proxy(s) => Pin::new(s).poll_next(cx),
        }
    }
}

/// Binance WebSocket 客户端
pub struct BinanceWsClient {
    state: SharedState,
}

impl BinanceWsClient {
    pub fn new(state: SharedState) -> Self {
        Self { state }
    }

    /// 通过代理连接WebSocket
    async fn connect_with_proxy(url: &Url, proxy_url: &str) -> Result<UnifiedStream> {
        use tokio::io::{AsyncReadExt, AsyncWriteExt};

        // 解析代理URL
        let proxy_url = Url::parse(proxy_url)?;
        let proxy_host = proxy_url
            .host_str()
            .ok_or_else(|| anyhow!("Invalid proxy host"))?;
        let proxy_port = proxy_url.port().unwrap_or(7890);

        info!(
            "Connecting through HTTP proxy {}:{}",
            proxy_host, proxy_port
        );

        // 建立到代理的TCP连接
        let proxy_addr = format!("{}:{}", proxy_host, proxy_port);
        let mut tcp_stream = TcpStream::connect(&proxy_addr).await?;

        // 发送HTTP CONNECT请求
        let target_host = url
            .host_str()
            .ok_or_else(|| anyhow!("Invalid target host"))?;
        let target_port = url.port().unwrap_or(443);

        let connect_request = format!(
            "CONNECT {}:{} HTTP/1.1\r\nHost: {}:{}\r\nProxy-Connection: keep-alive\r\n\r\n",
            target_host, target_port, target_host, target_port
        );

        info!("Sending CONNECT request to proxy");
        tcp_stream.write_all(connect_request.as_bytes()).await?;

        // 读取代理响应
        let mut buffer = [0; 1024];
        let n = tcp_stream.read(&mut buffer).await?;
        let response = String::from_utf8_lossy(&buffer[..n]);

        info!("Proxy response: {}", response.lines().next().unwrap_or(""));

        if !response.starts_with("HTTP/1.1 200") && !response.starts_with("HTTP/1.0 200") {
            return Err(anyhow!(
                "Proxy connection failed: {}",
                response.lines().next().unwrap_or("")
            ));
        }

        info!("Proxy tunnel established successfully");

        // 现在需要建立TLS连接，因为这是WSS (WebSocket Secure)
        // 直接使用client_async_tls来处理TLS和WebSocket握手
        info!("Establishing TLS and WebSocket connection through proxy tunnel");

        // 使用client_async_tls来处理TLS和WebSocket握手
        use tokio_tungstenite::client_async_tls;
        let (stream, _response) = client_async_tls(url.as_str(), tcp_stream)
            .await
            .map_err(|e| anyhow!("TLS WebSocket handshake failed: {}", e))?;

        info!("TLS WebSocket connection established successfully!");

        Ok(UnifiedStream::Proxy(stream))
    }

    #[cfg(test)]
    async fn test_connect_with_proxy_internal(url: &Url, proxy_url: &str) -> Result<()> {
        Self::connect_with_proxy(url, proxy_url).await?;
        Ok(())
    }

    /// 启动K线数据订阅
    pub async fn start_kline_streams(&self, symbol: &str) -> Result<()> {
        let spot_url = self.build_spot_kline_url(symbol)?;
        let futures_url = self.build_futures_kline_url(symbol)?;

        // 启动现货K线流
        let state_spot = self.state.clone();
        let spot_url_clone = spot_url.clone();
        tokio::spawn(async move {
            if let Err(e) = Self::connect_and_handle_stream(
                state_spot,
                spot_url_clone,
                false,
                "kline".to_string(),
            )
            .await
            {
                error!("Spot kline stream error: {}", e);
            }
        });

        // 启动期货K线流
        let state_futures = self.state.clone();
        let futures_url_clone = futures_url.clone();
        tokio::spawn(async move {
            if let Err(e) = Self::connect_and_handle_stream(
                state_futures,
                futures_url_clone,
                true,
                "kline".to_string(),
            )
            .await
            {
                error!("Futures kline stream error: {}", e);
            }
        });

        info!("Started Binance kline streams for symbol: {}", symbol);
        Ok(())
    }

    /// 启动深度数据订阅
    pub async fn start_depth_streams(&self, symbol: &str) -> Result<()> {
        info!("Starting depth streams for symbol: {}", symbol);

        // 首先启动深度差分流
        let spot_depth_url = self.build_spot_depth_url(symbol)?;
        info!("Built spot depth URL: {}", spot_depth_url);

        let futures_depth_url = self.build_futures_depth_url(symbol)?;
        info!("Built futures depth URL: {}", futures_depth_url);

        // 启动现货深度流
        let state_spot = self.state.clone();
        let spot_depth_url_clone = spot_depth_url.clone();
        tokio::spawn(async move {
            if let Err(e) = Self::connect_and_handle_stream(
                state_spot,
                spot_depth_url_clone,
                false,
                "depth".to_string(),
            )
            .await
            {
                error!("Spot depth stream error: {}", e);
            }
        });

        // 启动期货深度流
        let state_futures = self.state.clone();
        let futures_depth_url_clone = futures_depth_url.clone();
        tokio::spawn(async move {
            if let Err(e) = Self::connect_and_handle_stream(
                state_futures,
                futures_depth_url_clone,
                true,
                "depth".to_string(),
            )
            .await
            {
                error!("Futures depth stream error: {}", e);
            }
        });

        info!("Started Binance depth streams for symbol: {}", symbol);

        // 延迟获取深度快照，让差分流先连接
        let state_snapshot = self.state.clone();
        let symbol_snapshot = symbol.to_string();
        tokio::spawn(async move {
            // 等待2秒让WebSocket连接建立
            tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

            let client = BinanceWsClient::new(state_snapshot);
            if let Err(e) = client.fetch_depth_snapshots(&symbol_snapshot).await {
                error!("Failed to fetch depth snapshots: {}", e);
            }
        });

        Ok(())
    }

    /// 启动24小时ticker数据订阅
    pub async fn start_ticker_streams(&self, symbol: &str) -> Result<()> {
        info!("Starting 24hr ticker streams for symbol: {}", symbol);

        let spot_ticker_url = self.build_spot_ticker_url(symbol)?;
        let futures_ticker_url = self.build_futures_ticker_url(symbol)?;

        // 启动现货ticker流
        let state_spot = self.state.clone();
        let symbol_spot = symbol.to_string();
        tokio::spawn(async move {
            if let Err(e) =
                Self::handle_ticker_stream(spot_ticker_url, state_spot, symbol_spot, false).await
            {
                error!("Spot ticker stream error: {}", e);
            }
        });

        // 启动期货ticker流
        let state_futures = self.state.clone();
        let symbol_futures = symbol.to_string();
        tokio::spawn(async move {
            if let Err(e) =
                Self::handle_ticker_stream(futures_ticker_url, state_futures, symbol_futures, true)
                    .await
            {
                error!("Futures ticker stream error: {}", e);
            }
        });

        info!("Started Binance ticker streams for symbol: {}", symbol);
        Ok(())
    }

    /// 启动交易数据订阅
    pub async fn start_trade_streams(&self, symbol: &str) -> Result<()> {
        info!("Starting trade streams for symbol: {}", symbol);

        let spot_trade_url = self.build_spot_trade_url(symbol)?;
        let futures_trade_url = self.build_futures_trade_url(symbol)?;

        // 启动现货交易流
        let state_spot = self.state.clone();
        let spot_trade_url_clone = spot_trade_url.clone();
        tokio::spawn(async move {
            if let Err(e) = Self::connect_and_handle_stream(
                state_spot,
                spot_trade_url_clone,
                false,
                "trade".to_string(),
            )
            .await
            {
                error!("Spot trade stream error: {}", e);
            }
        });

        // 启动期货交易流
        let state_futures = self.state.clone();
        let futures_trade_url_clone = futures_trade_url.clone();
        tokio::spawn(async move {
            if let Err(e) = Self::connect_and_handle_stream(
                state_futures,
                futures_trade_url_clone,
                true,
                "trade".to_string(),
            )
            .await
            {
                error!("Futures trade stream error: {}", e);
            }
        });

        info!("Started Binance trade streams for symbol: {}", symbol);
        Ok(())
    }

    /// 构建现货K线WebSocket URL
    fn build_spot_kline_url(&self, symbol: &str) -> Result<String> {
        let stream = format!("{}@kline_1m", symbol.to_lowercase());
        // 尝试使用不同的端点，包括可能在国内可访问的
        Ok(format!("wss://stream.binance.com:9443/ws/{}", stream))
    }

    /// 构建期货K线WebSocket URL
    fn build_futures_kline_url(&self, symbol: &str) -> Result<String> {
        let stream = format!("{}@kline_1m", symbol.to_lowercase());
        // 尝试使用不同的端点，包括可能在国内可访问的
        Ok(format!("wss://fstream.binance.com/ws/{}", stream))
    }

    /// 构建现货深度WebSocket URL
    fn build_spot_depth_url(&self, symbol: &str) -> Result<String> {
        let stream = format!("{}@depth", symbol.to_lowercase());
        Ok(format!("wss://stream.binance.com:9443/ws/{}", stream))
    }

    /// 构建期货深度WebSocket URL
    fn build_futures_depth_url(&self, symbol: &str) -> Result<String> {
        let stream = format!("{}@depth", symbol.to_lowercase());
        Ok(format!("wss://fstream.binance.com/ws/{}", stream))
    }

    /// 构建现货24小时ticker WebSocket URL
    fn build_spot_ticker_url(&self, symbol: &str) -> Result<String> {
        let stream = format!("{}@ticker", symbol.to_lowercase());
        Ok(format!("wss://stream.binance.com:9443/ws/{}", stream))
    }

    /// 构建期货24小时ticker WebSocket URL
    fn build_futures_ticker_url(&self, symbol: &str) -> Result<String> {
        let stream = format!("{}@ticker", symbol.to_lowercase());
        Ok(format!("wss://fstream.binance.com/ws/{}", stream))
    }

    /// 构建现货交易WebSocket URL
    fn build_spot_trade_url(&self, symbol: &str) -> Result<String> {
        let stream = format!("{}@trade", symbol.to_lowercase());
        Ok(format!("wss://stream.binance.com:9443/ws/{}", stream))
    }

    /// 构建期货交易WebSocket URL
    fn build_futures_trade_url(&self, symbol: &str) -> Result<String> {
        let stream = format!("{}@aggTrade", symbol.to_lowercase());
        Ok(format!("wss://fstream.binance.com/ws/{}", stream))
    }

    /// 连接并处理WebSocket流
    async fn connect_and_handle_stream(
        state: SharedState,
        url: String,
        is_futures: bool,
        stream_type: String,
    ) -> Result<()> {
        loop {
            match Self::try_connect_and_handle(&state, &url, is_futures, &stream_type).await {
                Ok(_) => {
                    warn!("WebSocket connection closed, reconnecting...");
                }
                Err(e) => {
                    error!("WebSocket connection error: {}, retrying in 5s...", e);
                }
            }
            sleep(Duration::from_secs(5)).await;
        }
    }

    /// 尝试连接并处理WebSocket
    async fn try_connect_and_handle(
        state: &SharedState,
        url: &str,
        is_futures: bool,
        stream_type: &str,
    ) -> Result<()> {
        info!("Attempting to connect to {}", url);

        // 解析URL
        let url = Url::parse(url)?;

        // 连接WebSocket
        info!("Establishing WebSocket connection...");

        // 检查是否有代理设置
        let ws_stream = if let Ok(proxy_url) = env::var("https_proxy") {
            info!("Using proxy: {}", proxy_url);
            Self::connect_with_proxy(&url, &proxy_url).await?
        } else {
            info!("Connecting directly");
            let (stream, _response) = connect_async(&url).await?;
            UnifiedStream::Direct(stream)
        };

        info!("Successfully connected to Binance WebSocket!");

        let (mut write, mut read) = ws_stream.split();

        // 处理消息
        while let Some(msg) = read.next().await {
            match msg {
                Ok(Message::Text(text)) => {
                    if let Err(e) =
                        Self::handle_message(state, &text, is_futures, stream_type).await
                    {
                        error!("Error handling message: {}", e);
                    }
                }
                Ok(Message::Close(_)) => {
                    info!("WebSocket connection closed by server");
                    break;
                }
                Ok(Message::Ping(data)) => {
                    debug!("Received ping, sending pong");
                    if let Err(e) = write.send(Message::Pong(data)).await {
                        error!("Failed to send pong: {}", e);
                        break;
                    }
                }
                Ok(_) => {
                    // 忽略其他消息类型
                }
                Err(e) => {
                    error!("WebSocket error: {}", e);
                    break;
                }
            }
        }

        Ok(())
    }

    /// 处理ticker WebSocket流
    async fn handle_ticker_stream(
        url: String,
        state: SharedState,
        symbol: String,
        is_futures: bool,
    ) -> Result<()> {
        let market_type = if is_futures { "futures" } else { "spot" };
        info!(
            "Starting {} ticker stream for {}: {}",
            market_type, symbol, url
        );

        loop {
            debug!("Attempting to parse ticker URL: {}", url);
            let parsed_url = match url.parse::<Url>() {
                Ok(url) => url,
                Err(e) => {
                    error!("Failed to parse ticker URL {}: {}", url, e);
                    return Err(anyhow!("Failed to parse URL {}: {}", url, e));
                }
            };
            debug!("Successfully parsed ticker URL: {}", parsed_url);
            match Self::connect_with_proxy(&parsed_url, "127.0.0.1:7890").await {
                Ok(mut ws_stream) => {
                    info!("Connected to {} ticker stream for {}", market_type, symbol);

                    while let Some(msg) = ws_stream.next().await {
                        match msg {
                            Ok(Message::Text(text)) => {
                                if let Err(e) =
                                    Self::handle_message(&state, &text, is_futures, "ticker").await
                                {
                                    error!("Error handling {} ticker message: {}", market_type, e);
                                }
                            }
                            Ok(Message::Close(_)) => {
                                warn!("{} ticker stream closed for {}", market_type, symbol);
                                break;
                            }
                            Err(e) => {
                                error!("{} ticker stream error for {}: {}", market_type, symbol, e);
                                break;
                            }
                            _ => {}
                        }
                    }
                }
                Err(e) => {
                    error!(
                        "Failed to connect to {} ticker stream for {}: {}",
                        market_type, symbol, e
                    );
                }
            }

            // 重连延迟
            sleep(Duration::from_secs(5)).await;
            info!(
                "Reconnecting to {} ticker stream for {}...",
                market_type, symbol
            );
        }
    }

    /// 处理WebSocket消息
    async fn handle_message(
        state: &SharedState,
        text: &str,
        is_futures: bool,
        stream_type: &str,
    ) -> Result<()> {
        let data: Value = serde_json::from_str(text)?;

        match stream_type {
            "kline" => {
                // 检查是否是K线数据
                if let Some(event_type) = data.get("e").and_then(|v| v.as_str()) {
                    if event_type == "kline" {
                        if let Some(kline_data) = data.get("k") {
                            let kline = Self::parse_kline_data(kline_data)?;
                            let symbol = kline.symbol.clone();
                            state.add_kline(&symbol, kline, is_futures);

                            // 更新状态消息
                            let market_type = if is_futures { "Futures" } else { "Spot" };
                            state.set_status(format!(
                                "Updated {} kline for {}",
                                market_type, symbol
                            ));
                        }
                    }
                }
            }
            "depth" => {
                // 检查是否是深度差分数据
                if let Some(event_type) = data.get("e").and_then(|v| v.as_str()) {
                    if event_type == "depthUpdate" {
                        let depth_diff: DepthDiff = serde_json::from_str(text)?;
                        let symbol = &depth_diff.symbol;

                        if state.apply_depth_diff(symbol, &depth_diff, is_futures) {
                            // 更新状态消息
                            let market_type = if is_futures { "Futures" } else { "Spot" };
                            state.set_status(format!(
                                "Updated {} depth for {}",
                                market_type, symbol
                            ));
                        } else {
                            warn!(
                                "Failed to apply depth diff for {}, re-fetching snapshot",
                                symbol
                            );

                            // 重新获取快照
                            let state_refetch = state.clone();
                            let symbol_refetch = symbol.to_string();
                            info!(
                                "Spawning task to re-fetch {} depth snapshot for {}",
                                if is_futures { "futures" } else { "spot" },
                                symbol
                            );
                            tokio::spawn(async move {
                                info!(
                                    "Starting to re-fetch {} depth snapshot for {}",
                                    if is_futures { "futures" } else { "spot" },
                                    symbol_refetch
                                );
                                let client = BinanceWsClient::new(state_refetch);
                                if is_futures {
                                    if let Ok(futures_snapshot) =
                                        client.fetch_futures_depth_snapshot(&symbol_refetch).await
                                    {
                                        client.state.initialize_depth_snapshot(
                                            &symbol_refetch,
                                            futures_snapshot,
                                            true,
                                        );
                                        info!(
                                            "Re-initialized futures depth snapshot for {}",
                                            symbol_refetch
                                        );
                                    }
                                } else {
                                    if let Ok(spot_snapshot) =
                                        client.fetch_spot_depth_snapshot(&symbol_refetch).await
                                    {
                                        client.state.initialize_depth_snapshot(
                                            &symbol_refetch,
                                            spot_snapshot,
                                            false,
                                        );
                                        info!(
                                            "Re-initialized spot depth snapshot for {}",
                                            symbol_refetch
                                        );
                                    }
                                }
                            });
                        }
                    }
                }
            }
            "ticker" => {
                // 检查是否是24小时ticker数据
                if let Some(event_type) = data.get("e").and_then(|v| v.as_str()) {
                    if event_type == "24hrTicker" {
                        if let Ok(ticker_data) = Self::parse_ticker_data(&data) {
                            let symbol = ticker_data.0;
                            let volume = ticker_data.1;

                            // 更新24小时交易量数据
                            state.update_24h_volume_from_ticker(&symbol, volume, is_futures);

                            // 更新状态消息
                            let market_type = if is_futures { "Futures" } else { "Spot" };
                            state.set_status(format!(
                                "Updated {} 24h volume for {}: {:.2}",
                                market_type, symbol, volume
                            ));
                        }
                    }
                }
            }
            "trade" => {
                // 检查是否是交易数据
                if let Some(event_type) = data.get("e").and_then(|v| v.as_str()) {
                    if event_type == "trade" || event_type == "aggTrade" {
                        if let Ok(trade) = Self::parse_trade_data(&data) {
                            let symbol = trade.symbol.clone();

                            // 添加到对应的交易记录中
                            if is_futures {
                                state.add_futures_trade(&symbol, trade);
                            } else {
                                state.add_spot_trade(&symbol, trade);
                            }

                            // 更新状态消息
                            let market_type = if is_futures { "Futures" } else { "Spot" };
                            state.set_status(format!("New {} trade for {}", market_type, symbol));
                        }
                    }
                }
            }
            _ => {
                warn!("Unknown stream type: {}", stream_type);
            }
        }

        Ok(())
    }

    /// 解析K线数据
    fn parse_kline_data(kline_data: &Value) -> Result<Kline> {
        let symbol = kline_data
            .get("s")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing symbol"))?
            .to_string();

        let open_time = kline_data
            .get("t")
            .and_then(|v| v.as_i64())
            .ok_or_else(|| anyhow!("Missing open time"))?;

        let close_time = kline_data
            .get("T")
            .and_then(|v| v.as_i64())
            .ok_or_else(|| anyhow!("Missing close time"))?;

        let open_price = kline_data
            .get("o")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok())
            .ok_or_else(|| anyhow!("Missing or invalid open price"))?;

        let high_price = kline_data
            .get("h")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok())
            .ok_or_else(|| anyhow!("Missing or invalid high price"))?;

        let low_price = kline_data
            .get("l")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok())
            .ok_or_else(|| anyhow!("Missing or invalid low price"))?;

        let close_price = kline_data
            .get("c")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok())
            .ok_or_else(|| anyhow!("Missing or invalid close price"))?;

        let volume = kline_data
            .get("v")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok())
            .ok_or_else(|| anyhow!("Missing or invalid volume"))?;

        let quote_volume = kline_data
            .get("q")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok())
            .ok_or_else(|| anyhow!("Missing or invalid quote volume"))?;

        let trade_count = kline_data
            .get("n")
            .and_then(|v| v.as_i64())
            .ok_or_else(|| anyhow!("Missing trade count"))?;

        let taker_buy_volume = kline_data
            .get("V")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok())
            .ok_or_else(|| anyhow!("Missing or invalid taker buy volume"))?;

        let taker_buy_quote_volume = kline_data
            .get("Q")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok())
            .ok_or_else(|| anyhow!("Missing or invalid taker buy quote volume"))?;

        let is_closed = kline_data
            .get("x")
            .and_then(|v| v.as_bool())
            .ok_or_else(|| anyhow!("Missing is_closed flag"))?;

        Ok(Kline {
            symbol,
            open_time,
            close_time,
            open_price,
            high_price,
            low_price,
            close_price,
            volume,
            quote_volume,
            trade_count,
            taker_buy_volume,
            taker_buy_quote_volume,
            is_closed,
        })
    }

    /// 解析24小时ticker数据
    fn parse_ticker_data(data: &Value) -> Result<(String, f64)> {
        let symbol = data
            .get("s")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing symbol"))?
            .to_string();

        let volume = data
            .get("v")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok())
            .ok_or_else(|| anyhow!("Missing or invalid volume"))?;

        Ok((symbol, volume))
    }

    /// 解析交易数据
    fn parse_trade_data(data: &Value) -> Result<crate::types::Trade> {
        let symbol = data
            .get("s")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing symbol"))?
            .to_string();

        let price = data
            .get("p")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok())
            .ok_or_else(|| anyhow!("Missing or invalid price"))?;

        let qty = data
            .get("q")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok())
            .ok_or_else(|| anyhow!("Missing or invalid quantity"))?;

        let is_maker = data.get("m").and_then(|v| v.as_bool()).unwrap_or(false);
        let side = if is_maker {
            "SELL".to_string() // maker是卖方
        } else {
            "BUY".to_string() // taker是买方
        };

        let trade_id = data
            .get("t")
            .and_then(|v| v.as_u64())
            .map(|id| id.to_string())
            .unwrap_or_else(|| uuid::Uuid::new_v4().to_string());

        let timestamp = chrono::Utc::now();

        Ok(crate::types::Trade {
            id: trade_id,
            symbol,
            price,
            qty,
            side,
            timestamp,
            is_maker,
        })
    }

    /// 获取深度快照
    async fn fetch_depth_snapshots(&self, symbol: &str) -> Result<()> {
        // 获取现货深度快照
        if let Ok(spot_snapshot) = self.fetch_spot_depth_snapshot(symbol).await {
            self.state
                .initialize_depth_snapshot(symbol, spot_snapshot, false);
            info!("Initialized spot depth snapshot for {}", symbol);
        } else {
            warn!("Failed to fetch spot depth snapshot for {}", symbol);
        }

        // 获取期货深度快照
        if let Ok(futures_snapshot) = self.fetch_futures_depth_snapshot(symbol).await {
            self.state
                .initialize_depth_snapshot(symbol, futures_snapshot, true);
            info!("Initialized futures depth snapshot for {}", symbol);
        } else {
            warn!("Failed to fetch futures depth snapshot for {}", symbol);
        }

        Ok(())
    }

    /// 获取现货深度快照
    async fn fetch_spot_depth_snapshot(&self, symbol: &str) -> Result<DepthSnapshot> {
        let url = format!(
            "https://api.binance.com/api/v3/depth?symbol={}&limit=20",
            symbol.to_uppercase()
        );
        let response = reqwest::get(&url).await?;
        let data: Value = response.json().await?;

        let snapshot = DepthSnapshot {
            symbol: symbol.to_uppercase(),
            last_update_id: data
                .get("lastUpdateId")
                .and_then(|v| v.as_u64())
                .ok_or_else(|| anyhow!("Missing lastUpdateId"))?,
            bids: data
                .get("bids")
                .and_then(|v| v.as_array())
                .ok_or_else(|| anyhow!("Missing bids"))?
                .iter()
                .filter_map(|bid| {
                    if let (Some(price), Some(qty)) = (
                        bid.get(0).and_then(|v| v.as_str()),
                        bid.get(1).and_then(|v| v.as_str()),
                    ) {
                        Some((price.to_string(), qty.to_string()))
                    } else {
                        None
                    }
                })
                .collect(),
            asks: data
                .get("asks")
                .and_then(|v| v.as_array())
                .ok_or_else(|| anyhow!("Missing asks"))?
                .iter()
                .filter_map(|ask| {
                    if let (Some(price), Some(qty)) = (
                        ask.get(0).and_then(|v| v.as_str()),
                        ask.get(1).and_then(|v| v.as_str()),
                    ) {
                        Some((price.to_string(), qty.to_string()))
                    } else {
                        None
                    }
                })
                .collect(),
        };

        Ok(snapshot)
    }

    /// 获取期货深度快照
    async fn fetch_futures_depth_snapshot(&self, symbol: &str) -> Result<DepthSnapshot> {
        let url = format!(
            "https://fapi.binance.com/fapi/v1/depth?symbol={}&limit=20",
            symbol.to_uppercase()
        );
        let response = reqwest::get(&url).await?;
        let data: Value = response.json().await?;

        let snapshot = DepthSnapshot {
            symbol: symbol.to_uppercase(),
            last_update_id: data
                .get("lastUpdateId")
                .and_then(|v| v.as_u64())
                .ok_or_else(|| anyhow!("Missing lastUpdateId"))?,
            bids: data
                .get("bids")
                .and_then(|v| v.as_array())
                .ok_or_else(|| anyhow!("Missing bids"))?
                .iter()
                .filter_map(|bid| {
                    if let (Some(price), Some(qty)) = (
                        bid.get(0).and_then(|v| v.as_str()),
                        bid.get(1).and_then(|v| v.as_str()),
                    ) {
                        Some((price.to_string(), qty.to_string()))
                    } else {
                        None
                    }
                })
                .collect(),
            asks: data
                .get("asks")
                .and_then(|v| v.as_array())
                .ok_or_else(|| anyhow!("Missing asks"))?
                .iter()
                .filter_map(|ask| {
                    if let (Some(price), Some(qty)) = (
                        ask.get(0).and_then(|v| v.as_str()),
                        ask.get(1).and_then(|v| v.as_str()),
                    ) {
                        Some((price.to_string(), qty.to_string()))
                    } else {
                        None
                    }
                })
                .collect(),
        };

        Ok(snapshot)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio;

    #[tokio::test]
    async fn test_proxy_tunnel_only() {
        // 只测试代理隧道建立，不进行WebSocket握手
        use tokio::io::{AsyncReadExt, AsyncWriteExt};
        use tokio::net::TcpStream;

        let proxy_url = "http://127.0.0.1:7890";
        let target_host = "stream.binance.com";
        let target_port = 9443;

        println!("Testing proxy tunnel to: {}:{}", target_host, target_port);

        // 解析代理URL
        let proxy_url_parsed = Url::parse(proxy_url).expect("Failed to parse proxy URL");
        let proxy_host = proxy_url_parsed.host_str().unwrap();
        let proxy_port = proxy_url_parsed.port().unwrap_or(7890);

        // 连接到代理
        let proxy_addr = format!("{}:{}", proxy_host, proxy_port);
        println!("Connecting to proxy: {}", proxy_addr);

        match TcpStream::connect(&proxy_addr).await {
            Ok(mut tcp_stream) => {
                println!("✅ Connected to proxy");

                // 发送CONNECT请求
                let connect_request = format!(
                    "CONNECT {}:{} HTTP/1.1\r\nHost: {}:{}\r\nProxy-Connection: keep-alive\r\n\r\n",
                    target_host, target_port, target_host, target_port
                );

                println!("Sending CONNECT request...");
                if let Err(e) = tcp_stream.write_all(connect_request.as_bytes()).await {
                    println!("❌ Failed to send CONNECT request: {}", e);
                    return;
                }

                // 读取代理响应
                let mut buffer = [0; 1024];
                match tcp_stream.read(&mut buffer).await {
                    Ok(n) => {
                        let response = String::from_utf8_lossy(&buffer[..n]);
                        println!("Proxy response: {}", response.lines().next().unwrap_or(""));

                        if response.starts_with("HTTP/1.1 200") {
                            println!("✅ Proxy tunnel established successfully!");
                        } else {
                            println!(
                                "❌ Proxy tunnel failed: {}",
                                response.lines().next().unwrap_or("")
                            );
                        }
                    }
                    Err(e) => {
                        println!("❌ Failed to read proxy response: {}", e);
                    }
                }
            }
            Err(e) => {
                println!("❌ Failed to connect to proxy: {}", e);
            }
        }
    }

    #[tokio::test]
    async fn test_websocket_handshake_through_proxy() {
        // 测试完整的WebSocket握手过程
        let proxy_url = "http://127.0.0.1:7890";
        let ws_url = "wss://stream.binance.com:9443/ws/btcusdt@kline_1m";

        println!("Testing WebSocket handshake through proxy");
        println!("WebSocket URL: {}", ws_url);
        println!("Proxy URL: {}", proxy_url);

        let url = Url::parse(ws_url).expect("Failed to parse URL");

        match BinanceWsClient::connect_with_proxy(&url, proxy_url).await {
            Ok(_stream) => {
                println!("✅ WebSocket connection through proxy successful!");
            }
            Err(e) => {
                println!("❌ WebSocket connection through proxy failed: {}", e);
                // 不让测试失败，只是打印错误信息
            }
        }
    }

    #[tokio::test]
    async fn test_websocket_handshake_manual() {
        // 手动测试WebSocket握手，添加更多调试信息
        use tokio::io::{AsyncReadExt, AsyncWriteExt};
        use tokio::net::TcpStream;
        use tungstenite::handshake::client::generate_key;

        let proxy_url = "http://127.0.0.1:7890";
        let ws_url = "wss://stream.binance.com:9443/ws/btcusdt@kline_1m";

        println!("Manual WebSocket handshake test");

        let url = Url::parse(ws_url).expect("Failed to parse URL");

        // 解析代理URL
        let proxy_url_parsed = Url::parse(proxy_url).expect("Failed to parse proxy URL");
        let proxy_host = proxy_url_parsed.host_str().unwrap();
        let proxy_port = proxy_url_parsed.port().unwrap_or(7890);

        // 连接到代理
        let proxy_addr = format!("{}:{}", proxy_host, proxy_port);
        let mut tcp_stream = TcpStream::connect(&proxy_addr)
            .await
            .expect("Failed to connect to proxy");

        // 建立代理隧道
        let target_host = url.host_str().unwrap();
        let target_port = url.port().unwrap_or(443);

        let connect_request = format!(
            "CONNECT {}:{} HTTP/1.1\r\nHost: {}:{}\r\nProxy-Connection: keep-alive\r\n\r\n",
            target_host, target_port, target_host, target_port
        );

        tcp_stream
            .write_all(connect_request.as_bytes())
            .await
            .expect("Failed to send CONNECT");

        // 读取代理响应
        let mut buffer = [0; 1024];
        let n = tcp_stream
            .read(&mut buffer)
            .await
            .expect("Failed to read proxy response");
        let response = String::from_utf8_lossy(&buffer[..n]);

        println!("Proxy response: {}", response.lines().next().unwrap_or(""));
        assert!(response.starts_with("HTTP/1.1 200"), "Proxy tunnel failed");

        // 现在进行WebSocket握手
        let host = url.host_str().unwrap();
        let path = url.path();
        let query = url.query().map(|q| format!("?{}", q)).unwrap_or_default();
        let websocket_key = generate_key();

        let ws_request = format!(
            "GET {}{} HTTP/1.1\r\n\
             Host: {}\r\n\
             Upgrade: websocket\r\n\
             Connection: Upgrade\r\n\
             Sec-WebSocket-Key: {}\r\n\
             Sec-WebSocket-Version: 13\r\n\
             \r\n",
            path, query, host, websocket_key
        );

        println!("WebSocket request:");
        println!("{}", ws_request);

        tcp_stream
            .write_all(ws_request.as_bytes())
            .await
            .expect("Failed to send WebSocket request");

        // 读取WebSocket响应
        let mut buffer = [0; 4096];
        let n = tcp_stream
            .read(&mut buffer)
            .await
            .expect("Failed to read WebSocket response");
        let response = String::from_utf8_lossy(&buffer[..n]);

        println!("WebSocket response:");
        println!("{}", response);

        if response.contains("HTTP/1.1 101") {
            println!("✅ WebSocket handshake successful!");
        } else {
            println!("❌ WebSocket handshake failed");
        }
    }
}
